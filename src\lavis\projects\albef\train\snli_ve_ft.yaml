 # Copyright (c) 2022, salesforce.com, inc.
 # All rights reserved.
 # SPDX-License-Identifier: BSD-3-Clause
 # For full license text, see the LICENSE file in the repo root or https://opensource.org/licenses/BSD-3-Clause

model:
  arch: albef_classification
  model_type: ve
  load_finetuned: False
  num_classes: 3

datasets:
  snli_ve: # name of the dataset builder
    vis_processor:
        train:
          name: "blip_image_train"
        eval:
          name: "blip_image_eval"
    text_processor:
        train:
          name: "blip_caption"
        eval:
          name: "blip_caption"

run:
  task: multimodal_classification
  # optimization-specific
  lr_sched: "linear_warmup_cosine_lr"
  init_lr: 2e-5
  min_lr: 0
  weight_decay: 0.05
  max_epoch: 10
  batch_size_train: 32
  batch_size_eval: 64
  num_workers: 4

  seed: 42
  output_dir: "output/ALBEF/SNLI_VE"

  amp: False
  resume_ckpt_path: null

  evaluate: False 
  train_splits: ["train"]
  valid_splits: ["val"]
  test_splits: ["test"]

  # distribution-specific
  device: "cuda"
  world_size: 1
  dist_url: "env://"
  distributed: True
