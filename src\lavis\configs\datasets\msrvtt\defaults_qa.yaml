 # Copyright (c) 2022, salesforce.com, inc.
 # All rights reserved.
 # SPDX-License-Identifier: BSD-3-Clause
 # For full license text, see the LICENSE file in the repo root or https://opensource.org/licenses/BSD-3-Clause

datasets:
  msrvtt_qa: # name of the dataset builder
    # data_dir: ${env.data_dir}/datasets
    data_type: videos # [images|videos|features]

    build_info:
      # Be careful not to append minus sign (-) before split to avoid itemizing
      annotations:
        train:
          url: https://storage.googleapis.com/sfr-vision-language-research/LAVIS/datasets/msrvtt/qa_train.json
          storage: msrvtt/annotations/qa_train.json
        val:
          url: https://storage.googleapis.com/sfr-vision-language-research/LAVIS/datasets/msrvtt/qa_val.json
          storage: msrvtt/annotations/qa_val.json
        test:
          url: https://storage.googleapis.com/sfr-vision-language-research/LAVIS/datasets/msrvtt/qa_test.json
          storage: msrvtt/annotations/qa_test.json
        ans2label:
          url: https://storage.googleapis.com/sfr-vision-language-research/LAVIS/datasets/msrvtt/train_ans2label.json
          storage: msrvtt/annotations/qa_ans2label.json
      videos:
        storage: msrvtt/videos
