#!/bin/bash
# 训练脚本 - 使用Lrm损失函数 + 困难负样本损失函数
# dataset: CIRR或FashionIQ
# noise_ratio: 噪声比例（简化版仍可设置，但对训练无实际影响）
# gpu: 指定要使用的GPU设备编号（0, 1, 2...）

your_exp_name=lrm_with_hard_negative-7.7_0.5_0.5
shuffle_seed=42
seed=42
dataset=CIRR
# 指定使用的GPU编号，注意这里直接使用数字，不要加引号
gpu=0

noise_ratio=0.0

# 困难负样本损失参数
hard_negative_loss=robust_infoNCE
hard_negative_weight=0.5

echo "指定使用GPU: ${gpu}"
echo "困难负样本损失类型: ${hard_negative_loss}"
echo "困难负样本损失权重: ${hard_negative_weight}"

python src/precompute_train.py \
    --exp_name "${your_exp_name}" \
    --shuffle_seed ${shuffle_seed} \
    --seed ${seed} \
    --dataset ${dataset} \
    --noise_ratio ${noise_ratio} \
    --batch_size 64 \
    --num_epochs 30 \
    --lr "1e-5" \
    --positive_loss "RCL" \
    --trade_off 1.0 \
    --hard_negative_loss ${hard_negative_loss} \
    --hard_negative_weight ${hard_negative_weight} \
    --save_training \
    --gpu ${gpu}