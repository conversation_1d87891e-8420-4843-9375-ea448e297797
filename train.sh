#!/bin/bash
# 简化版训练脚本 - 只使用Lrm损失函数
# dataset: CIRR或FashionIQ
# noise_ratio: 噪声比例（简化版仍可设置，但对训练无实际影响）
# gpu: 指定要使用的GPU设备编号（0, 1, 2...）

your_exp_name=lrm_only-7.7-remove
shuffle_seed=42
seed=42 
dataset=FashionIQ
# 指定使用的GPU编号，注意这里直接使用数字，不要加引号
gpu=1

noise_ratio=0.0

echo "指定使用GPU: ${gpu}"

python src/precompute_train.py \
    --exp_name "${your_exp_name}" \
    --shuffle_seed ${shuffle_seed} \
    --seed ${seed} \
    --dataset ${dataset} \
    --noise_ratio ${noise_ratio} \
    --batch_size 64 \
    --num_epochs 30 \
    --lr "1e-5" \
    --positive_loss "RCL" \
    --trade_off 1.0 \
    --save_training \
    --gpu ${gpu}