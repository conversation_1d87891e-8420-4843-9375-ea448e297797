{"architectures": ["Bert<PERSON><PERSON><PERSON>"], "attention_probs_dropout_prob": 0.1, "hidden_act": "gelu", "hidden_dropout_prob": 0.1, "hidden_size": 768, "initializer_range": 0.02, "intermediate_size": 3072, "layer_norm_eps": 1e-12, "max_position_embeddings": 512, "model_type": "bert", "num_attention_heads": 12, "num_hidden_layers": 12, "pad_token_id": 0, "add_type_embeddings": false, "vocab_size": 30522, "encoder_width": 768, "add_cross_attention": true}