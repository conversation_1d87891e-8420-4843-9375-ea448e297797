 # Copyright (c) 2022, salesforce.com, inc.
 # All rights reserved.
 # SPDX-License-Identifier: BSD-3-Clause
 # For full license text, see the LICENSE file in the repo root or https://opensource.org/licenses/BSD-3-Clause

model:
  arch: albef_pretrain

  load_pretrained: True
  pretrained: "https://storage.googleapis.com/sfr-pcl-data-research/ALBEF/ALBEF.pth"

  # vit encoder
  vit_type: "base"
  image_size: 224
  vit_ckpt_layer: 0
  vit_drop_path_rate: 0
  vit_layer_norm_epsilon: 1e-6
  vit_grad_ckpt: False

  # bert config
  med_config_path: "configs/models/med_config_albef.json"
  mlm_mask_prob: 0.15

  embed_dim: 256
  momentum: 0.995
  alpha: 0.4
  temp: 0.07

  max_txt_len: 30

preprocess:
    vis_processor:
        train:
          name: "blip_image_train"
          image_size: 256
    text_processor:
        train:
          name: "blip_caption"
