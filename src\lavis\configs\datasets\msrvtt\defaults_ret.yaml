 # Copyright (c) 2022, salesforce.com, inc.
 # All rights reserved.
 # SPDX-License-Identifier: BSD-3-Clause
 # For full license text, see the LICENSE file in the repo root or https://opensource.org/licenses/BSD-3-Clause

datasets:
  msrvtt_retrieval: # name of the dataset builder
    # data_dir: ${env.data_dir}/datasets
    data_type: videos # [images|videos|features]

    build_info:
      # Be careful not to append minus sign (-) before split to avoid itemizing
      annotations:
        train:
          url: https://storage.googleapis.com/sfr-vision-language-research/LAVIS/datasets/msrvtt/retrieval_train.json
          storage: msrvtt/annotations/retrieval_train.json
        val:
          url: https://storage.googleapis.com/sfr-vision-language-research/LAVIS/datasets/msrvtt/retrieval_val.json
          storage: msrvtt/annotations/retrieval_val.json
        test:
          url: https://storage.googleapis.com/sfr-vision-language-research/LAVIS/datasets/msrvtt/retrieval_test.json
          storage: msrvtt/annotations/retrieval_test.json
      videos:
        storage: msrvtt/videos
