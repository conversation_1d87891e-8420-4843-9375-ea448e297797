 # Copyright (c) 2022, salesforce.com, inc.
 # All rights reserved.
 # SPDX-License-Identifier: BSD-3-Clause
 # For full license text, see the LICENSE file in the repo root or https://opensource.org/licenses/BSD-3-Clause

model:
  arch: albef_retrieval
  model_type: coco
  load_finetuned: False

  queue_size: 65536

datasets:
  coco_retrieval: # name of the dataset builder
    vis_processor:
        train:
          name: "blip_image_train"
          image_size: 384
        eval:
          name: "blip_image_eval"
          image_size: 384
    text_processor:
        train:
          name: "blip_caption"
        eval:
          name: "blip_caption"

run:
  task: retrieval
  # optimizer
  lr_sched: "linear_warmup_cosine_lr"
  init_lr: 1e-5
  min_lr: 1e-6
  weight_decay: 0.05
  max_epoch: 5

  # dataloading
  num_workers: 4
  batch_size_train: 32
  batch_size_eval: 64

  train_splits: ["train"]
  valid_splits: ["val"]
  test_splits: ["test"]

  # distribution
  device: "cuda"
  world_size: 1
  dist_url: "env://"
  distributed: True
  use_dist_eval_sampler: False

  # model specific
  k_test: 256

  # misc
  seed: 42
  output_dir: "output/ALBEF/Retrieval_COCO"

  amp: False
  resume_ckpt_path: null

  evaluate: False 
