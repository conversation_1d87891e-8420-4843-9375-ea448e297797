 # Copyright (c) 2022, salesforce.com, inc.
 # All rights reserved.
 # SPDX-License-Identifier: BSD-3-Clause
 # For full license text, see the LICENSE file in the repo root or https://opensource.org/licenses/BSD-3-Clause

model:
  arch: blip_caption
  load_finetuned: True

  pretrained: "https://storage.googleapis.com/sfr-vision-language-research/BLIP/models/model_base_capfilt_large.pth"
  finetuned: "https://storage.googleapis.com/sfr-vision-language-research/LAVIS/models/BLIP/blip_coco_caption_base.pth"

  # vit encoder
  vit_type: "base"
  vit_grad_ckpt: False
  vit_ckpt_layer: 0

  image_size: 384

  # bert config
  med_config_path: "configs/models/med_config.json"

  # generation configs
  prompt: "a picture of "


preprocess:
    vis_processor:
        train:
          name: "blip_image_train"
        eval:
          name: "blip_image_eval"
    text_processor:
        train:
          name: "blip_caption"
          prompt: "a picture of "
        eval:
          name: "blip_caption"
