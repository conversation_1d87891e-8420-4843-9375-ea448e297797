 # Copyright (c) 2022, salesforce.com, inc.
 # All rights reserved.
 # SPDX-License-Identifier: BSD-3-Clause
 # For full license text, see the LICENSE file in the repo root or https://opensource.org/licenses/BSD-3-Clause

datasets:
  snli_ve:
    # data_dir: ${env.data_dir}/datasets
    data_type: images # [images|videos|features]

    build_info:
      # Be careful not to append minus sign (-) before split to avoid itemizing
      annotations:
        train:
          url: /export/share/dongxuli/data/lavis/snli/annotation/ve_train.json
          storage: snli/annotations/ve_train.json
        val:
          url: /export/share/dongxuli/data/lavis/snli/annotation/ve_dev.json
          storage: snli/annotations/ve_dev.json
        test:
          url: /export/share/dongxuli/data/lavis/snli/annotation/ve_test.json
          storage: snli/annotations/ve_test.json
      images:
          storage: flickr30k/images/flickr30k-images
          # storage: /export/share/datasets/vision/flickr30k/flickr30k-images
