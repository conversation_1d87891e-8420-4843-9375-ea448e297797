 # Copyright (c) 2022, salesforce.com, inc.
 # All rights reserved.
 # SPDX-License-Identifier: BSD-3-Clause
 # For full license text, see the LICENSE file in the repo root or https://opensource.org/licenses/BSD-3-Clause

model:
  arch: blip_retrieval
  load_finetuned: True

  finetuned: "https://storage.googleapis.com/sfr-vision-language-research/LAVIS/models/BLIP/blip_coco_retrieval.pth"
  pretrained: "https://storage.googleapis.com/sfr-vision-language-research/BLIP/models/model_base_capfilt_large.pth"

  queue_size: 57600

  # vit encoder
  vit_type: "base"
  vit_grad_ckpt: True
  vit_ckpt_layer: 4

  image_size: 384

  # bert config
  med_config_path: "configs/models/med_config.json"

  embed_dim: 256

preprocess:
  vis_processor:
      train:
        name: "blip_image_train"
        image_size: 384
      eval:
        name: "blip_image_eval"
        image_size: 384
  text_processor:
      train:
        name: "blip_caption"
      eval:
        name: "blip_caption"
