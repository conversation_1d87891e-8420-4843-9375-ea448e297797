 # Copyright (c) 2022, salesforce.com, inc.
 # All rights reserved.
 # SPDX-License-Identifier: BSD-3-Clause
 # For full license text, see the LICENSE file in the repo root or https://opensource.org/licenses/BSD-3-Clause

datasets:
  sbu_caption:
    # data_dir: ${env.data_dir}/datasets
    data_type: images # [images|videos|features]

    build_info:
      # Be careful not to append minus sign (-) before split to avoid itemizing
      annotations:
        train:
          url:
              - https://storage.googleapis.com/sfr-vision-language-research/LAVIS/datasets/sbu/sbu.json
              # - /export/share/dongxuli/data/lavis/sbu/annotation/sbu.json
          storage:
              - sbu_captions/annotations/sbu.json
      images:
          storage: sbu_captions/images
          # storage: /export/share/datasets/vision_language/sbu_resize
