 # Copyright (c) 2022, salesforce.com, inc.
 # All rights reserved.
 # SPDX-License-Identifier: BSD-3-Clause
 # For full license text, see the LICENSE file in the repo root or https://opensource.org/licenses/BSD-3-Clause

model:
  arch: albef_retrieval
  model_type: flickr

datasets:
  flickr30k: # name of the dataset builder
    vis_processor:
        eval:
          name: "blip_image_eval"
          image_size: 384
    text_processor:
        eval:
          name: "blip_caption"

run:
  task: retrieval

  # dataloading
  num_workers: 4
  batch_size_train: 32
  batch_size_eval: 64

  test_splits: ["test"]

  # distribution
  device: "cuda"
  world_size: 1
  dist_url: "env://"
  distributed: True
  use_dist_eval_sampler: False

  # model specific
  k_test: 128

  # misc
  seed: 42
  output_dir: "output/ALBEF/Retrieval_Flickr30k"

  evaluate: True
