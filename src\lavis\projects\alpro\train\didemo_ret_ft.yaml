 # Copyright (c) 2022, salesforce.com, inc.
 # All rights reserved.
 # SPDX-License-Identifier: BSD-3-Clause
 # For full license text, see the LICENSE file in the repo root or https://opensource.org/licenses/BSD-3-Clause

model:
  arch: alpro_retrieval
  model_type: didemo
  load_finetuned: False

  max_txt_len: 50

  timesformer:
    n_frms: 8
    image_size: 224


datasets:
  didemo_retrieval: # name of the dataset builder
    vis_processor:
        train:
          name: "alpro_video_train"
          n_frms: 8
          image_size: 224
        eval:
          name: "alpro_video_eval"
          n_frms: 8
          image_size: 224
    text_processor:
        train:
          name: "blip_caption"
        eval:
          name: "blip_caption"

run:
  task: retrieval
  # optimization-specific
  lr_sched: "linear_warmup_cosine_lr"
  init_lr: 3e-5
  min_lr: 1e-6
  weight_decay: 1e-4
  max_epoch: 10
  batch_size_train: 12
  batch_size_eval: 32
  num_workers: 4

  k_test: 256
  # k_test: 1000

  seed: 42
  output_dir: "output/ALPRO/didemo_retrieval"

  amp: False
  resume_ckpt_path: null

  evaluate: False 
  train_splits: ["train"]
  valid_splits: ["val"]
  test_splits: ["test"]

  # distribution-specific
  device: "cuda"
  world_size: 1
  dist_url: "env://"
  distributed: True
  use_dist_eval_sampler: False
