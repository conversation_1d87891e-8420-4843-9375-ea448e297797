 # Copyright (c) 2022, salesforce.com, inc.
 # All rights reserved.
 # SPDX-License-Identifier: BSD-3-Clause
 # For full license text, see the LICENSE file in the repo root or https://opensource.org/licenses/BSD-3-Clause

model:
  arch: blip_pretrain

  load_pretrained: True
  pretrained: "https://storage.googleapis.com/sfr-vision-language-research/BLIP/models/model_base_capfilt_large.pth"

  # vit encoder
  vit_type: "base"
  vit_grad_ckpt: False
  vit_ckpt_layer: 0

  image_size: 224
  alpha: 0.4

  # bert config
  med_config_path: "configs/models/bert_config.json"

  embed_dim: 256

  # generation configs
  prompt: "a picture of "

preprocess:
    vis_processor:
        train:
          name: "blip_image_train"
          image_size: 224
    text_processor:
        train:
          name: "blip_caption"
