import pathlib
import os
from typing import List
import torchvision.transforms.functional as TF
from torchvision.transforms import Compose, Resize, CenterCrop, ToTensor, Normalize, InterpolationMode
import torch
import torch.nn.functional as F
import logging
import time
import sys
from sklearn.mixture import GaussianMixture
from tqdm import tqdm
import numpy as np
import json
import copy
import random


dir_path = os.path.dirname(os.path.dirname(__file__))
base_path = pathlib.Path(dir_path)

device = torch.device('cuda')


def generate_randomized_fiq_caption(flattened_captions: List[str]) -> List[str]:
    """
    Function which randomize the FashionIQ training captions in four way: (a) cap1 and cap2 (b) cap2 and cap1 (c) cap1
    (d) cap2
    :param flattened_captions: the list of caption to randomize, note that the length of such list is 2*batch_size since
     to each triplet are associated two captions
    :return: the randomized caption list (with length = batch_size)
    """
    captions = []
    for i in range(0, len(flattened_captions), 2):
        random_num = random.random()
        if random_num < 0.25:
            captions.append(
                f"{flattened_captions[i].strip('.?, ').capitalize()} and {flattened_captions[i + 1].strip('.?, ')}")
        elif 0.25 < random_num < 0.5:
            captions.append(
                f"{flattened_captions[i + 1].strip('.?, ').capitalize()} and {flattened_captions[i].strip('.?, ')}")
        elif 0.5 < random_num < 0.75:
            captions.append(f"{flattened_captions[i].strip('.?, ').capitalize()}")
        else:
            captions.append(f"{flattened_captions[i + 1].strip('.?, ').capitalize()}")
    return captions


def set_device(i):
    global device
    logging.info(f"尝试设置GPU设备为: cuda:{i}")
    try:
        # 确保i是整数
        i = int(i)
        # 设置CUDA设备
        torch.cuda.set_device(i)
        if torch.cuda.is_available():
            device = torch.device(f'cuda:{i}')
            logging.info(f"成功设置设备为: {device}")
            logging.info(f"当前活跃的CUDA设备: {torch.cuda.current_device()}")
        else:
            device = torch.device('cpu')
            logging.info("CUDA不可用，使用CPU")
    except Exception as e:
        logging.error(f"设置GPU设备时出错: {e}")
        if torch.cuda.is_available():
            device = torch.device('cuda:0')
            logging.info(f"出错后使用默认设备: {device}")
        else:
            device = torch.device('cpu')
            logging.info("CUDA不可用，使用CPU")


def get_closs(i2t, target, loss_name=None):
    loss = torch.tensor(0.).to(i2t.device)
    bs = i2t.shape[0]
    if bs == 0:
        return loss
    if loss_name == 'None' or loss_name is None:
        return loss
    if loss_name == 'RCL':
        mask = torch.ones_like(i2t).to(float).to(i2t.device)
        mask[torch.arange(bs), target] = 0.
        loss = - ((1. - i2t).log() * mask).sum() / bs
        return loss
    if loss_name == 'infoNCE':
        mask = torch.zeros_like(i2t).to(float).to(i2t.device)
        mask[torch.arange(bs), target] = 1.
        loss = - (i2t.log() * mask).sum() / bs
        return loss
    raise ValueError('loss name is invalid')

def get_aloss(left, right, loss_name=None):
    bs = left.shape[0]
    loss = torch.tensor(0.).to(left.device)
    mse_criterion = torch.nn.MSELoss()
    sml1_criterion = torch.nn.SmoothL1Loss()
    if bs == 0:
        return loss
    if loss_name is None or loss_name == 'None':
        return loss
    if loss_name == 'MSE':
        # loss = ((right - left) ** 2).sum(1).mean()
        loss = mse_criterion(left, right)
        return loss
    if loss_name == 'SmoothL1':
        loss = sml1_criterion(left, right)
        return loss
    raise ValueError('loss name is invalid')

def robust_mse(left, right, labels, pn_loss):
    clean_mask = labels.to(bool)
    noise_mask = ~clean_mask
    ploss = get_aloss(left[clean_mask], right[clean_mask], pn_loss['positive_align_loss'])
    nloss = get_aloss(left[noise_mask], right[noise_mask], pn_loss['negative_align_loss'])
    trade_off = pn_loss['trade_off_align']
    loss_dca = trade_off * ploss + (1-trade_off) * nloss
    return loss_dca

def hard_negative_loss(sim_matrix, labels, loss_name='infoNCE', margin=0.2, top_k=5):
    """
    计算困难负样本损失函数
    Args:
        sim_matrix: 相似度矩阵 [batch_size, batch_size]
        labels: 标签，通常是对角线为正样本 [batch_size]
        loss_name: 损失函数类型 ('infoNCE', 'triplet', 'RCL')
        margin: triplet loss的margin参数
        top_k: 选择top-k个困难负样本
    Returns:
        hard_negative_loss: 困难负样本损失
    """
    device = sim_matrix.device
    batch_size = sim_matrix.shape[0]

    if batch_size == 0:
        return torch.tensor(0.).to(device)

    # 创建正样本mask (对角线)
    positive_mask = torch.eye(batch_size, dtype=torch.bool, device=device)

    # 获取正样本相似度
    positive_sim = sim_matrix[positive_mask]  # [batch_size]

    # 创建负样本mask (非对角线)
    negative_mask = ~positive_mask

    # 对于每个样本，选择困难负样本
    hard_neg_loss = torch.tensor(0.).to(device)

    for i in range(batch_size):
        # 获取当前样本的负样本相似度
        neg_sims = sim_matrix[i][negative_mask[i]]  # [batch_size-1]

        if len(neg_sims) == 0:
            continue

        # 选择top-k个最高相似度的负样本作为困难负样本
        k = min(top_k, len(neg_sims))
        hard_neg_sims, _ = torch.topk(neg_sims, k)  # [k]

        pos_sim = positive_sim[i]  # 当前样本的正样本相似度

        if loss_name == 'triplet':
            # Triplet loss: max(0, margin + hard_neg_sim - pos_sim)
            triplet_losses = torch.clamp(margin + hard_neg_sims - pos_sim, min=0)
            hard_neg_loss += triplet_losses.mean()

        elif loss_name == 'infoNCE':
            # InfoNCE with hard negatives
            # 将正样本和困难负样本组合
            logits = torch.cat([pos_sim.unsqueeze(0), hard_neg_sims])  # [k+1]
            target = torch.tensor(0, device=device)  # 正样本在第0位
            hard_neg_loss += F.cross_entropy(logits.unsqueeze(0), target.unsqueeze(0))

        elif loss_name == 'RCL':
            # RCL (Reverse Cross-entropy Loss) with hard negatives
            # 对困难负样本应用RCL
            probs = torch.softmax(torch.cat([pos_sim.unsqueeze(0), hard_neg_sims]), dim=0)
            # RCL: -log(1 - prob_negative)
            neg_probs = probs[1:]  # 负样本概率
            rcl_loss = -torch.log(1 - neg_probs + 1e-7).mean()
            hard_neg_loss += rcl_loss

    return hard_neg_loss / batch_size

class TargetPad:
    """
    Pad the image if its aspect ratio is above a target ratio.
    Pad the image to match such target ratio
    """

    def __init__(self, target_ratio: float, size: int):
        """
        :param target_ratio: target ratio
        :param size: preprocessing output dimension
        """
        self.size = size
        self.target_ratio = target_ratio

    def __call__(self, image):
        w, h = image.size
        actual_ratio = max(w, h) / min(w, h)
        if actual_ratio < self.target_ratio:  # check if the ratio is above or below the target ratio
            return image
        scaled_max_wh = max(w, h) / self.target_ratio  # rescale the pad to match the target ratio
        hp = max(int((scaled_max_wh - w) / 2), 0)
        vp = max(int((scaled_max_wh - h) / 2), 0)
        padding = [hp, vp, hp, vp]
        return TF.pad(image, padding, 0, 'constant')
    
def _convert_image_to_rgb(image):
    return image.convert("RGB")   

def targetpad_transform(target_ratio: float, dim: int):
    """
    CLIP-like preprocessing transform computed after using TargetPad pad
    :param target_ratio: target ratio for TargetPad
    :param dim: image output dimension
    :return: CLIP-like torchvision Compose transform
    """
    return Compose([
        TargetPad(target_ratio, dim),
        Resize(dim, interpolation=InterpolationMode.BICUBIC),
        CenterCrop(dim),
        _convert_image_to_rgb,
        ToTensor(),
        Normalize((0.48145466, 0.4578275, 0.40821073), (0.26862954, 0.26130258, 0.27577711)),
    ])
    
def get_log(dataset_name, exp_name):
    """
    设置日志系统并创建日志文件夹。
    
    Args:
        dataset_name: 数据集名称
        exp_name: 实验名称
        
    Returns:
        tuple: (日志文件夹路径, 时间戳)
    """
    # 创建基本目录结构
    if not os.path.exists(base_path /'log_TME'):
        os.makedirs(base_path / 'log_TME', exist_ok=True)
    if not os.path.exists(base_path / 'log_TME' / dataset_name):
        os.makedirs(base_path / 'log_TME' / dataset_name, exist_ok=True)
    
    # 生成时间戳和日志文件夹路径
    timestamp = time.strftime('%Y-%m-%d %H_%M_%S', time.localtime(time.time()))
    if exp_name:
        log_folder_path = base_path / 'log_TME' / dataset_name / exp_name
    else:
        log_folder_path = base_path / 'log_TME' / dataset_name / timestamp
    
    # 确保日志文件夹存在
    os.makedirs(log_folder_path, exist_ok=True)
    print(f"创建日志文件夹: {log_folder_path}")
    
    # 重置日志系统，防止多次调用添加多个处理器
    for handler in logging.root.handlers[:]:
        logging.root.removeHandler(handler)
    
    # 设置日志格式
    log_format = '%(asctime)s: %(message)s'
    logging.basicConfig(
        level=logging.INFO,
        format=log_format,
        datefmt='%Y-%m-%d %H:%M:%S',
        handlers=[
            logging.StreamHandler(sys.stdout),  # 输出到控制台
        ]
    )
    
    # 添加文件处理器
    try:
        log_file_path = log_folder_path / 'process.log'
        print(f"日志将被保存到: {log_file_path}")
        file_handler = logging.FileHandler(log_file_path, mode='a')
        file_handler.setFormatter(logging.Formatter(log_format, datefmt='%Y-%m-%d %H:%M:%S'))
        logging.getLogger().addHandler(file_handler)
        logging.info(f"=== 训练开始于 {timestamp} ===")
    except Exception as e:
        print(f"警告: 创建日志文件处理器失败: {e}")
    
    return log_folder_path, timestamp

def get_log_simple(file_path):
    log_format = '%(asctime)s: %(message)s'
    date_format = '%Y-%m-%d-%H-%M-%S'
    logging.basicConfig(stream=sys.stdout, level=logging.INFO, format=log_format, datefmt=date_format)
    os.makedirs(os.path.dirname(file_path), exist_ok=True)
    fh = logging.FileHandler(file_path)
    fh.setFormatter(logging.Formatter(log_format, datefmt=date_format))
    logging.getLogger().addHandler(fh)
    return file_path

class Params:
    bool_initialize = False
    @staticmethod
    def initialize(args):
        global params
        if Params.bool_initialize:
            raise ValueError('params have been initialized.')
        if args is None:
            raise ValueError('params list should not be None')
        print('Initialize params')
        params.set_params(args)
        Params.bool_initialize = True
    
    def __init__(self):
        pass
    
    def set_params(self, args):
        # basic params:
        self.dataset = args.dataset
        self.method = getattr(args, 'method', 'image_diff')  # 设置默认值
        self.noise_ratio = args.noise_ratio
        self.nc_type = getattr(args, 'nc_type', 'mix')  # 设置默认值

        self.save_training = args.save_training
        # basic setting
        self.backbone = args.backbone
        self.num_workers = args.num_workers
        self.weight_decay = args.weight_decay
        self.lr = args.lr
        self.batch_size = args.batch_size
        self.num_epochs = args.num_epochs
        self.seed = args.seed
        self.shuffle_seed = args.shuffle_seed
        
        self.timestamp = args.timestamp
        self.debug = getattr(args, 'debug', False)  # 设置默认值
        
        # method setting - 简化后的损失设置
        self.pn_loss = {}
        self.pn_loss['positive_loss'] = args.positive_loss
        self.pn_loss['negative_loss'] = args.negative_loss
        self.pn_loss['trade_off'] = args.trade_off

        # 为缺失的参数设置默认值
        self.pn_loss['positive_align_loss'] = 'None'
        self.pn_loss['negative_align_loss'] = 'None'
        self.pn_loss['trade_off_align'] = 1.0
        self.pn_loss['warmup_loss'] = args.positive_loss  # 使用与positive_loss相同的值
        self.pn_loss['warmup_align_loss'] = 'None'

        # 困难负样本损失参数
        self.hard_negative_loss = getattr(args, 'hard_negative_loss', 'None')
        self.hard_negative_weight = getattr(args, 'hard_negative_weight', 0.1)
        self.hard_negative_margin = getattr(args, 'hard_negative_margin', 0.2)
        self.hard_negative_top_k = getattr(args, 'hard_negative_top_k', 5)
        
        # 为缺失的损失权重设置默认值
        self.lrm = 1.0
        self.lpm = 0.0
        self.lsa = 0.0
        self.lrd = 0.0
        
        # 为缺失的warmup参数设置默认值
        self.warmup_qformer = 0
        self.warmup_proj = 0
        self.warmup_last = 0
        self.warmup_epoch = 0
        
        # 为缺失的partitioner参数设置默认值
        self.partitioner = 'all_positive'
        self.split_type = 'loss'
        self.threshold = 0.5
        
        # experiment_name
        self.exp_name = args.exp_name
        
    def __call__(self):
        display_dict = copy.deepcopy(self.__dict__)
        keys_to_remove = ['num_workers', 'timestamp']
        for key in keys_to_remove:
            del display_dict[key]
        return display_dict
    
params = Params()

class Partitioner:
    
    def __init__(self, type, split, threshold=0.5, timestamp=None, epoch=None, dataset_name=None):
        self.type = type
        self.split = split
        self.threshold = threshold
        # self.debug = debug
        self.timestamp = timestamp
        self.epoch = epoch
        self.dataset_name = dataset_name
        
    def fit_features(self, model, trainloader, txt_processors, debug=False):
        dataset = trainloader.dataset
        if self.type == 'all_positive':
            logging.info('no partition, all positive')
            return torch.ones(len(dataset)) # all clean
        logging.info('fitting partitioner...')
        model.eval()
        data_size = len(dataset)
        loss = torch.zeros(data_size)
        sim = torch.zeros(data_size)
        for reference_name, target_name, captions, index in tqdm(trainloader, ncols=150, mininterval=30):
            reference_images = dataset.get_image_features(reference_name).to(device, non_blocking=True)
            target_images = dataset.get_image_features(target_name).to(device, non_blocking=True)
            if self.dataset_name == 'FashionIQ':
                    flattened_captions = np.array(captions).T.flatten().tolist()
                    captions = generate_randomized_fiq_caption(flattened_captions)
            captions = [txt_processors['eval'](caption) for caption in captions]
            l, s = model.per_loss(reference_images, target_images, captions)
            for b in range(l.size(0)):
                loss[index[b]] = l[b]
                sim[index[b]] = s[b]
        self.losses = (loss-loss.min())/(loss.max()-loss.min())
        self.sims = (sim-sim.min())/(sim.max()-sim.min())
        self.pred = self.get_pred(self.type, debug=debug)
        return self.pred
            
    # Previous version: not use precomputed features. 
    def fit(self, model, trainloader, txt_processors):
        if self.type == 'all_positive':
            logging.info('no partition, all positive')
            return torch.ones(len(trainloader.dataset)) # all clean
        logging.info('fitting partitioner...')
        model.eval()
        data_size = len(trainloader.dataset)
        loss = torch.zeros(data_size)
        sim = torch.zeros(data_size)
        # gt_labels = torch.zeros(data_size)
        with tqdm(total=len(trainloader), mininterval=30) as t:
            for i, data in enumerate(trainloader):
                reference_image = data['source_img_data'].to(device, non_blocking=True)
                target_image = data['target_img_data'].to(device, non_blocking=True)
                captions = data['mod']['str']
                if self.dataset_name == 'FashionIQ':
                    flattened_captions = np.array(captions).T.flatten().tolist()
                    captions = generate_randomized_fiq_caption(flattened_captions)
                captions = [txt_processors['eval'](caption) for caption in captions]
                index = data['index']
                # gt_label = data['gt_label']
                l, s = model.per_loss(reference_image, target_image, captions)
                for b in range(l.size(0)):
                    loss[index[b]] = l[b]
                    sim[index[b]] = s[b]
                    # gt_labels[index[b]] = gt_label[b]
                t.update()
        # self.gt_labels = gt_labels
        self.losses = (loss-loss.min())/(loss.max()-loss.min())
        self.sims = (sim-sim.min())/(sim.max()-sim.min())
        self.pred = self.get_pred(self.type)       
        return self.pred
    
    def get_pred(self, type, threshold=None, debug=False):
        type = type.lower()
        if threshold is None:
            threshold = self.threshold
        if type.lower() == 'gmm':
            input_loss = self.losses.reshape(-1,1) 
            input_sim = self.sims.reshape(-1,1)
            input_data = input_loss if self.split == 'loss' else input_sim
            # probability computation
        
            gmm = GaussianMixture(n_components=2, max_iter=10, tol=1e-2, reg_covar=5e-4)
            gmm.fit(input_data.cpu().numpy())
            clean_component_idx = gmm.means_.argmin() if self.split == 'loss' else gmm.means_.argmax()
            self.prob = torch.Tensor(gmm.predict_proba(input_data.cpu().numpy())[:, clean_component_idx])
            
            self.pred = (self.prob > threshold) + 0
            if debug:
                # clean_rate = (self.pred == self.gt_labels).sum() / len(self.pred)
                # print(f'the clean partition rate is {clean_rate}')
                save_path = f'partitioner_log/{self.timestamp}'
                if not os.path.exists(save_path):
                    os.makedirs(save_path)
                torch.save(self.losses, f'{save_path}/loss_{self.epoch}.pth')
                torch.save(self.sims, f'{save_path}/sim_{self.epoch}.pth')
                torch.save(self.prob, f'{save_path}/prob_{self.epoch}.pth')
                exit(0)
            area_num = torch.histc(torch.tensor(self.prob), bins=10, min=0.0, max=1.0).to(torch.int).tolist()
            logging.info(f'The counts in the equal areas are: {area_num}')
            clean_pro = self.pred.sum().item() / self.pred.shape[0]
            logging.info(f'the proportion of clean samples are {clean_pro}')
            return self.pred
        elif type == 'direct':
            if self.split == 'loss':
                input_data = self.losses
            elif self.split == 'sim':
                input_data = self.sims
            else:
                raise ValueError(f"the parameter split is invalid.")
            self.pred = (input_data < threshold) + 0
            self.prob = self.pred
            print('the proportion of clean samples are ', self.pred.sum().item() / self.pred.shape[0])
            return self.pred
        elif type == 'percent':
            if self.split == 'loss':
                input_data = self.losses
            elif self.split == 'sim':
                input_data = self.sims
            else:
                raise ValueError(f"the parameter split is invalid.")
            noisy_indices = input_data.argsort(descending=True)[:int(threshold * input_data.shape[0])]
            self.pred = torch.ones_like(input_data)
            self.pred[noisy_indices] = 0
            self.prob = self.pred
            print('the proportion of clean samples are ', self.pred.sum().item() / self.pred.shape[0])
            return self.pred
        else:
            raise ValueError(f"the parameter type is invalid.")
        
    def get_prob(self):
        if self.prob is None:
            raise KeyError('prob does not exist')
        else:
            return self.prob
        
def custom_json_dumps(data, indent=4):
    def serialize(obj, indent_level=0):
        if isinstance(obj, dict):
            items = []
            for key, value in obj.items():
                items.append(f'\n{" " * indent * (indent_level + 1)}"{key}": {serialize(value, indent_level + 1)}')
            return f'{{{",".join(items)}\n{" " * indent * indent_level}}}'
        elif isinstance(obj, list):
            items = [json.dumps(item, indent=0) for item in obj]
            return f'[{", ".join(items)}]'
        else:
            return json.dumps(obj)

    return serialize(data)