 # Copyright (c) 2022, salesforce.com, inc.
 # All rights reserved.
 # SPDX-License-Identifier: BSD-3-Clause
 # For full license text, see the LICENSE file in the repo root or https://opensource.org/licenses/BSD-3-Clause

model:
  arch: albef_retrieval
  load_finetuned: True

  pretrained: "https://storage.googleapis.com/sfr-pcl-data-research/ALBEF/ALBEF.pth"
  finetuned: https://storage.googleapis.com/sfr-vision-language-research/LAVIS/models/ALBEF/albef_flickr_retrieval_lavis.pt

  queue_size: 65536

  # vit encoder
  vit_type: "base"
  image_size: 384
  vit_ckpt_layer: 0
  vit_drop_path_rate: 0
  vit_layer_norm_epsilon: 1e-6
  vit_grad_ckpt: False

  # bert config
  med_config_path: "configs/models/med_config_albef.json"

  embed_dim: 256
  momentum: 0.995
  alpha: 0.4
  temp: 0.07
  use_distill: True

  max_txt_len: 30

preprocess:
  vis_processor:
      train:
        name: "blip_image_train"
        image_size: 384
      eval:
        name: "blip_image_eval"
        image_size: 384
  text_processor:
      train:
        name: "blip_caption"
      eval:
        name: "blip_caption"
