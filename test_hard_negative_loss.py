#!/usr/bin/env python3
"""
测试困难负样本损失函数的简单脚本
"""

import torch
import sys
import os

# 添加src目录到路径
sys.path.append(os.path.join(os.path.dirname(__file__), 'src'))

from utility import hard_negative_loss

def test_hard_negative_loss():
    """测试困难负样本损失函数"""
    print("测试困难负样本损失函数...")
    
    # 创建测试数据
    batch_size = 4
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    
    # 创建相似度矩阵
    sim_matrix = torch.randn(batch_size, batch_size, device=device)
    # 让对角线元素更大（正样本相似度更高）
    sim_matrix = sim_matrix + torch.eye(batch_size, device=device) * 2.0
    
    labels = torch.arange(batch_size, device=device)
    
    print(f"相似度矩阵:\n{sim_matrix}")
    print(f"标签: {labels}")
    
    # 测试不同的损失函数
    loss_types = ['infoNCE', 'triplet', 'RCL']
    
    for loss_type in loss_types:
        try:
            loss = hard_negative_loss(
                sim_matrix=sim_matrix,
                labels=labels,
                loss_name=loss_type,
                margin=0.2,
                top_k=2
            )
            print(f"{loss_type} 困难负样本损失: {loss.item():.4f}")
        except Exception as e:
            print(f"{loss_type} 损失计算出错: {e}")
    
    print("测试完成!")

if __name__ == "__main__":
    test_hard_negative_loss()
