 # Copyright (c) 2022, salesforce.com, inc.
 # All rights reserved.
 # SPDX-License-Identifier: BSD-3-Clause
 # For full license text, see the LICENSE file in the repo root or https://opensource.org/licenses/BSD-3-Clause

model:
  arch: gpt_dialogue
  model_type: base

datasets:
  avsd_dialogue: # name of the dataset builder
    vis_processor:
        eval:
          name: "gpt_video_ft"
          visual_ft: ["i3d_flow", "i3d_rgb"]
          audio_ft: ["vggish"]
    text_processor:
        eval:
          name: "gpt_dialogue"
          max_turns:  3
          use_caption: True

run:
  task: dialogue
  # optimizer
  batch_size_train: 16
  batch_size_eval: 16
  num_workers: 0

  max_len: 20
  min_len: 5
  num_beams: 5

  seed: 42
  output_dir: "output/gpt2/dialogue_avsd"

  evaluate: True
  valid_splits: ["test"]

  device: "cuda"
  world_size: 1
  dist_url: "env://"
  distributed: True
