 # Copyright (c) 2022, salesforce.com, inc.
 # All rights reserved.
 # SPDX-License-Identifier: BSD-3-Clause
 # For full license text, see the LICENSE file in the repo root or https://opensource.org/licenses/BSD-3-Clause

# Overall Accuracy is: 41.22

model:
  arch: blip2_t5
  model_type: pretrain_flant5xl
  use_grad_checkpoint: False

  # for OKVQA evaluation
  apply_lemmatizer: True

datasets:
  ok_vqa: # name of the dataset builder
    vis_processor:
        eval:
          name: "blip_image_eval"
          image_size: 224
    text_processor:
        eval:
          name: "blip_question"
#     build_info:
#         images:
#             storage: '/export/share/datasets/vision/coco/images/'

run:
  task: vqa
  # optimization-specific
  batch_size_train: 16
  batch_size_eval: 64
  num_workers: 4

  # inference-specific
  max_len: 10
  min_len: 1
  num_beams: 5
  inference_method: "generate"
  prompt: "Question: {} Short answer:"

  seed: 42
  output_dir: "output/BLIP2/OKVQA"

  evaluate: True
  test_splits: ["test"]

  # distribution-specific
  device: "cuda"
  world_size: 1
  dist_url: "env://"
  distributed: True
