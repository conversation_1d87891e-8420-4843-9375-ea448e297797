 # Copyright (c) 2022, salesforce.com, inc.
 # All rights reserved.
 # SPDX-License-Identifier: BSD-3-Clause
 # For full license text, see the LICENSE file in the repo root or https://opensource.org/licenses/BSD-3-Clause

model:
  arch: blip_caption
  model_type: large

datasets:
  coco_caption: # name of the dataset builder
    vis_processor:
        train:
          name: "blip_image_train"
        eval:
          name: "blip_image_eval"
    text_processor:
        train:
          name: "blip_caption"
          prompt: "a picture of "
        eval:
          name: "blip_caption"

run:
  runner: runner_iter

  max_iters: 2e4
  iters_per_inner_epoch: 2e3

  # task: retrieval
  task: captioning
  # optimizer
  lr_sched: "linear_warmup_cosine_lr"
  init_lr: 2e-6
  min_lr: 0
  weight_decay: 0.05
  batch_size_train: 16
  batch_size_eval: 64
  num_workers: 4

  max_len: 20
  min_len: 5
  num_beams: 3

  seed: 42
  output_dir: "output/BLIP/Caption_coco"

  amp: False
  resume_ckpt_path: null

  evaluate: False 
  train_splits: ["train"]
  valid_splits: ["val", "test"]

  device: "cuda"
  world_size: 1
  dist_url: "env://"
  distributed: True
