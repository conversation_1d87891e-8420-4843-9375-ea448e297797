 # Copyright (c) 2022, salesforce.com, inc.
 # All rights reserved.
 # SPDX-License-Identifier: BSD-3-Clause
 # For full license text, see the LICENSE file in the repo root or https://opensource.org/licenses/BSD-3-Clause

model:
  arch: blip_nlvr
  model_type: nlvr

datasets:
  nlvr: # name of the dataset builder
    vis_processor:
        eval:
          name: "blip_image_eval"
          image_size: 384
    text_processor:
        eval:
          name: "blip_caption"

run:
  task: multimodal_classification

  batch_size_train: 16
  batch_size_eval: 64
  num_workers: 4

  seed: 42
  output_dir: "output/BLIP/NLVR"

  evaluate: True
  test_splits: ["val", "test"]

  # distribution-specific
  device: "cuda"
  world_size: 1
  dist_url: "env://"
  distributed: True
