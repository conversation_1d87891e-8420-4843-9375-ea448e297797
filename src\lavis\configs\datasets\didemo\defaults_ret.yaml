 # Copyright (c) 2022, salesforce.com, inc.
 # All rights reserved.
 # SPDX-License-Identifier: BSD-3-Clause
 # For full license text, see the LICENSE file in the repo root or https://opensource.org/licenses/BSD-3-Clause

datasets:
  didemo_retrieval: # name of the dataset builder
    # data_dir: ${env.data_dir}/datasets
    data_type: videos # [images|videos|features]

    build_info:
      # Be careful not to append minus sign (-) before split to avoid itemizing
      annotations:
        train:
          url: https://storage.googleapis.com/sfr-vision-language-research/LAVIS/datasets/didemo/retrieval_train.json
          storage: didemo/annotations/retrieval_train.json
        val:
          url: https://storage.googleapis.com/sfr-vision-language-research/LAVIS/datasets/didemo/retrieval_val.json
          storage: didemo/annotations/retrieval_val.json
        test:
          url: https://storage.googleapis.com/sfr-vision-language-research/LAVIS/datasets/didemo/retrieval_test.json
          storage: didemo/annotations/retrieval_test.json
      videos:
        storage: didemo/videos
        # storage: /export/share/dongxuli/data/didemo_retrieval/videos
