 # Copyright (c) 2022, salesforce.com, inc.
 # All rights reserved.
 # SPDX-License-Identifier: BSD-3-Clause
 # For full license text, see the LICENSE file in the repo root or https://opensource.org/licenses/BSD-3-Clause

datasets:
  nocaps: # name of the dataset builder
    # data_dir: ${env.data_dir}/datasets
    data_type: images # [images|videos|features]

    build_info:
      # Be careful not to append minus sign (-) before split to avoid itemizing
      annotations:
        val:
          url: https://storage.googleapis.com/sfr-vision-language-research/datasets/nocaps_val.json
          storage:  nocaps/annotations/nocaps_val.json
        test:
          url: https://storage.googleapis.com/sfr-vision-language-research/datasets/nocaps_test.json
          storage: nocaps/annotations/nocaps_test.json
      images:
        storage: nocaps/images
        # storage: /export/share/datasets/vision/nocaps/
