 # Copyright (c) 2022, salesforce.com, inc.
 # All rights reserved.
 # SPDX-License-Identifier: BSD-3-Clause
 # For full license text, see the LICENSE file in the repo root or https://opensource.org/licenses/BSD-3-Clause

model:
  arch: blip_nlvr
  model_type: nlvr
  load_finetuned: True

  finetuned: "https://storage.googleapis.com/sfr-vision-language-research/BLIP/models/model_base_nlvr.pth"
  pretrained: "https://storage.googleapis.com/sfr-vision-language-research/BLIP/models/model_base_capfilt_large.pth"

  num_classes: 2

  # vit encoder
  vit_type: "base"
  vit_grad_ckpt: False
  vit_ckpt_layer: 0
  vit_layer_norm_epsilon: 1e-6

  image_size: 384

  # bert config
  med_config_path: "configs/models/med_config.json"

preprocess:
  vis_processor:
      train:
        name: "blip_image_train"
        image_size: 384
      eval:
        name: "blip_image_eval"
        image_size: 384
  text_processor:
      train:
        name: "blip_caption"
      eval:
        name: "blip_caption"
