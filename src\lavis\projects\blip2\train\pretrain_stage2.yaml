 # Copyright (c) 2022, salesforce.com, inc.
 # All rights reserved.
 # SPDX-License-Identifier: BSD-3-Clause
 # For full license text, see the LICENSE file in the repo root or https://opensource.org/licenses/BSD-3-Clause

model:
  arch: blip2_opt
  model_type: pretrain_opt2.7b 
  load_pretrained: True
  # intialize stage 2 pretraining from stage 1 pretrained model
  pretrained: "https://storage.googleapis.com/sfr-vision-language-research/LAVIS/models/BLIP2/blip2_pretrained.pth"
  freeze_vit: True


datasets:
  coco_caption:
    vis_processor:
        train:
          name: "blip2_image_train"
          image_size: 224
    text_processor:
        train:
          name: "blip_caption"
    # build_info:
    #     images:
    #         storage: '/export/share/datasets/vision/coco/images/'          
  vg_caption: # name of the dataset builder
    vis_processor:
        train:
          name: "blip_image_train"
          image_size: 224
    text_processor:
        train:
          name: "blip_caption"
    # build_info:
    #     images:
    #         storage: '//export/share/datasets/vision/visual-genome/image/'

run:
  task: image_text_pretrain
  # optimizer
  lr_sched: "linear_warmup_cosine_lr"
  init_lr: 1e-4
  min_lr: 1e-5
  warmup_lr: 1e-6

  weight_decay: 0.05
  max_epoch: 10
  batch_size_train: 64
  batch_size_eval: 64
  num_workers: 4
  warmup_steps: 2000

  seed: 42
  output_dir: "output/BLIP2/Pretrain_stage2"

  amp: True
  resume_ckpt_path: null

  evaluate: False 
  train_splits: ["train"]

  device: "cuda"
  world_size: 1
  dist_url: "env://"
  distributed: True