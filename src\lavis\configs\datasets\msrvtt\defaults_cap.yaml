 # Copyright (c) 2022, salesforce.com, inc.
 # All rights reserved.
 # SPDX-License-Identifier: BSD-3-Clause
 # For full license text, see the LICENSE file in the repo root or https://opensource.org/licenses/BSD-3-Clause

datasets:
  msrvtt_cap: # name of the dataset builder
    # data_dir: ${env.data_dir}/datasets
    data_type: videos # [images|videos|features]

    build_info:
      # Be careful not to append minus sign (-) before split to avoid itemizing
      annotations:
        train:
          url: https://storage.googleapis.com/sfr-vision-language-research/LAVIS/datasets/msrvtt/cap_train.json
          storage: msrvtt/annotations/cap_train.json
        val:
          url: https://storage.googleapis.com/sfr-vision-language-research/LAVIS/datasets/msrvtt/cap_val.json
          storage: msrvtt/annotations/cap_val.json
        test:
          url: https://storage.googleapis.com/sfr-vision-language-research/LAVIS/datasets/msrvtt/cap_test.json
          storage: msrvtt/annotations/cap_test.json
      videos:
        storage: msrvtt/videos
